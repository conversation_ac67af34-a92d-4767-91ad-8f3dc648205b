const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../../../ai/AiService');
const ToolExecutor = require('../../../ai/tools/ToolExecutor');
const PromptBuilder = require('../../../ai/PromptBuilder');
const BuildErrorAnalyzer = require('./BuildErrorAnalyzer');
const PromptResponseHandler = require('../../../ai/PromptResponseHandler');
const VueFileValidator = require('../../../frameworks/vue/VueFileValidator');
const StringReplaceTool = require('../../../ai/tools/StringReplaceTool');

class BuildFixAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath || process.cwd(), 'ai-logs')
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.options = {
      maxAttempts: 10,
      dryRun: false,
      verbose: false,
      ...options
    };

    // 初始化重构后的工具执行器
    this.toolExecutor = new ToolExecutor(projectPath, this.options);

    // 初始化提示词构建器
    this.promptBuilder = new PromptBuilder(this.toolExecutor.getToolRegistry(), this.options);

    // 初始化响应处理器
    this.responseHandler = new PromptResponseHandler(this.options);

    // 初始化字符串替换工具
    this.stringReplaceTool = new StringReplaceTool(this.options);

    // 初始化错误分析器
    this.errorAnalyzer = new BuildErrorAnalyzer(projectPath, this, this.toolExecutor, this.options);

    // 设置ErrorAnalyzer的PromptBuilder
    this.errorAnalyzer.setPromptBuilder(this.promptBuilder);

    // 重复检测
    this.attemptHistory = {
      filesToFix: [], // 记录每次尝试修复的文件列表
      errorHashes: [], // 记录每次的错误哈希
      lastErrorOutput: null // 记录上次的错误输出
    };

    // 修复统计
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
  }

  async fixFiles(filesToFix, buildOutput, attemptNumber = 1, suggestions = []) {
    let filesModified = 0;
    const errors = [];

    const previousAttempts = this.getPreviousAttempts(filesToFix, attemptNumber);

    for (let fileIndex = 0; fileIndex < filesToFix.length; fileIndex++) {
      const filePath = filesToFix[fileIndex];
      try {
        console.log(chalk.gray(`🔧 修复文件: ${filePath}`));

        // 读取文件内容
        const fileResult = await this.toolExecutor.executeToolCall('read_file', { file_path: filePath });

        if (!fileResult.success) {
          console.log(chalk.yellow(`  ⚠️  无法读取文件: ${fileResult.error}`));
          errors.push(`无法读取文件 ${filePath}: ${fileResult.error}`);
          continue;
        }

        const filePreviousAttempts = previousAttempts.filter(attempt => attempt.filePath === filePath);

        const relevantSuggestions = this.findRelevantSuggestions(filePath, suggestions);
        const fixResult = await this.fixSingleFile(
          filePath,
          fileResult.content,
          buildOutput,
          attemptNumber,
          fileIndex + 1,
          filesToFix.length,
          filePreviousAttempts,
          relevantSuggestions
        );

        if (fixResult.success) {
          const writeResult = await this.toolExecutor.executeToolCall('write_file', {
            file_path: filePath,
            content: fixResult.fixedContent
          });

          if (writeResult.success) {
            console.log(chalk.green('  ✅ 文件修复成功'));
            filesModified++;
            this.fixStats.filesModified++;
          } else {
            console.log(chalk.yellow(`  ⚠️  无法写入文件: ${writeResult.error}`));
            errors.push(`无法写入文件 ${filePath}: ${writeResult.error}`);
          }
        } else {
          console.log(chalk.yellow(`  ⚠️  AI 修复失败: ${fixResult.error}`));

          // 显示 AI 响应的部分内容用于调试
          if (fixResult.aiResponse) {
            console.log(chalk.gray(`     AI 响应预览: ${fixResult.aiResponse.substring(0, 200)}...`));
          }

          // 如果是解析错误，显示更多调试信息
          if (fixResult.error.includes('无法解析AI响应格式')) {
            console.log(chalk.gray(`     🔍 调试信息: 响应长度 ${fixResult.aiResponse ? fixResult.aiResponse.length : 0} 字符`));
            if (this.options.verbose && fixResult.aiResponse) {
              console.log(chalk.gray(`     📝 完整响应内容:`));
              console.log(chalk.gray(fixResult.aiResponse));
            }
          }

          errors.push(`AI 修复失败 ${filePath}: ${fixResult.error}`);

          // 记录失败的尝试
          this.recordFailedAttempt(filePath, attemptNumber, fixResult.error);
        }
      } catch (error) {
        console.log(chalk.red(`  ❌ 修复文件异常: ${error.message}`));
        errors.push(`修复文件异常 ${filePath}: ${error.message}`);
        this.recordFailedAttempt(filePath, attemptNumber, error.message);
      }
    }

    this.fixStats.attempts++;
    this.fixStats.filesAnalyzed += filesToFix.length;

    return {
      success: filesModified > 0,
      filesModified,
      errors: errors.length > 0 ? errors : undefined,
      totalFiles: filesToFix.length
    };
  }

  /**
   * 执行工具调用（委托给ToolExecutor）
   */
  async executeToolCall(toolName, parameters) {
    return await this.toolExecutor.executeToolCall(toolName, parameters);
  }

  /**
   * 找到与文件相关的建议
   */
  findRelevantSuggestions(filePath, suggestions) {
    if (!suggestions || suggestions.length === 0) {
      return [];
    }

    return suggestions.filter(suggestion => {
      // 检查错误信息中是否包含文件路径
      if (suggestion.error && suggestion.error.includes(filePath)) {
        return true;
      }

      // 检查文件扩展名是否匹配建议类型
      const ext = path.extname(filePath);
      if (suggestion.type === 'scss_undefined_variable' && (ext === '.scss' || ext === '.sass')) {
        return true;
      }
      if (suggestion.type === 'vue_deep_selector' && ext === '.vue') {
        return true;
      }
      if (suggestion.type === 'typescript_error' && (ext === '.ts' || ext === '.tsx' || ext === '.vue')) {
        return true;
      }

      return false;
    });
  }

  /**
   * 修复单个文件 - 两轮AI调用模式
   * 第一轮：根据错误生成工具调用，决定需要读取哪些文件
   * 第二轮：基于读取的文件内容和错误信息，生成具体的修复代码
   */
  async fixSingleFile(filePath, fileContent, buildOutput, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = [], suggestions = []) {
    try {
      const toolCalls = await this.generateToolCalls(filePath, buildOutput, attemptNumber, previousAttempts, suggestions);

      if (!toolCalls || toolCalls.length === 0) {
        return await this.generateFileFix(
          filePath, fileContent, buildOutput, {},
          attemptNumber, fileIndex, totalFiles, previousAttempts, suggestions
        );
      }

      const contextFiles = await this.executeToolCalls(toolCalls);

      return await this.generateFileFix(
        filePath, fileContent, buildOutput, contextFiles,
        attemptNumber, fileIndex, totalFiles, previousAttempts, suggestions
      );
    } catch (error) {
      return {
        success: false,
        error: error.message,
        filePath
      };
    }
  }

  /**
   * 修复运行时错误
   * 专门处理Vue运行时错误，包括组件错误、响应式数据问题等
   */
  async fixRuntimeError(errorContext) {
    try {
      const { fileName, message, stack, componentTrace, buildOutput } = errorContext;

      if (this.options.verbose) {
        console.log(chalk.gray(`🔧 开始修复运行时错误: ${fileName}`));
      }

      // 读取出错文件内容
      const filePath = path.resolve(this.projectPath, fileName);
      if (!await fs.pathExists(filePath)) {
        throw new Error(`文件不存在: ${fileName}`);
      }

      const fileContent = await fs.readFile(filePath, 'utf8');

      // 使用专门的运行时错误修复流程
      const fixResult = await this.fixRuntimeErrorFile(
        fileName, fileContent, errorContext
      );

      if (this.options.verbose) {
        console.log(chalk.gray(`       🔍 修复结果: success=${fixResult.success}, hasNewContent=${!!fixResult.newContent}, hasFixedContent=${!!fixResult.fixedContent}`));
      }

      // 检查修复结果
      const fixedContent = fixResult.newContent || fixResult.fixedContent;

      if (fixResult.success && fixedContent) {
        // 应用修复
        if (!this.options.dryRun) {
          await fs.writeFile(filePath, fixedContent, 'utf8');
          console.log(chalk.green(`✅ 运行时错误修复已应用: ${fileName}`));
        } else {
          console.log(chalk.blue(`🔍 [DRY RUN] 运行时错误修复预览: ${fileName}`));
        }

        this.fixStats.filesModified++;
        this.fixStats.errorsFixed++;

        return {
          success: true,
          fixedContent: fixedContent,
          newContent: fixedContent
        };
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ❌ 修复失败: ${fixResult.error || '未知错误'}`));
        }
        return fixResult;
      }

    } catch (error) {
      console.error(chalk.red(`运行时错误修复失败: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 修复运行时错误文件
   */
  async fixRuntimeErrorFile(fileName, fileContent, errorContext) {
    try {
      const context = {
        taskType: 'runtime-error-fix',
        errorType: errorContext.type || 'runtime',
        fileName,
        message: errorContext.message,
        stack: errorContext.stack,
        componentTrace: errorContext.componentTrace
      };

      const prompt = this.promptBuilder.buildRuntimeErrorFixPrompt(
        fileName, fileContent, errorContext, context
      );

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'runtime-error-fix',
          phase: 'fix-generation',
          fileName: fileName,
          errorType: errorContext.type
        }
      });

      return this.parseFixResponse(response, fileContent);
    } catch (error) {
      return {
        success: false,
        error: error.message,
        aiResponse: error.response || null
      };
    }
  }

  async generateToolCalls(filePath, buildOutput, attemptNumber = 1, previousAttempts = [], suggestions = []) {
    try {
      const context = {
        attemptNumber,
        previousAttempts,
        taskType: 'tool-call-generation',
        suggestions
      };

      const prompt = this.promptBuilder.buildToolCallPrompt(filePath, buildOutput, context);

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'tool-call-generation',
          attemptNumber: attemptNumber,
          phase: 'tool-calls',
          fileName: filePath
        }
      });

      return this.responseHandler.parseToolCallsResponse(response);
    } catch (error) {
      console.log(chalk.yellow(`⚠️  生成工具调用失败: ${error.message}`));
      return [];
    }
  }

  async executeToolCalls(toolCalls) {
    try {
      const result = await this.toolExecutor.executeToolCalls(toolCalls);

      if (result.success) {
        return this.toolExecutor.formatContextFiles(result.results);
      } else {
        console.log(chalk.yellow(`⚠️  工具调用执行失败: ${result.errors?.join(', ')}`));
        return {};
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️  工具调用执行异常: ${error.message}`));
      return {};
    }
  }

  /**
   * 第二轮AI调用：基于收集的文件内容生成修复代码
   */
  async generateFileFix(filePath, fileContent, buildOutput, contextFiles, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = [], suggestions = []) {
    try {
      this.currentFilePath = filePath;

      const context = {
        attemptNumber,
        fileIndex,
        totalFiles,
        previousAttempts,
        taskType: 'file-fix',
        suggestions
      };

      const shouldUseStrReplace = this.shouldUseStringReplace(fileContent, filePath);
      if (shouldUseStrReplace) {
        if (this.options.verbose) {
          console.log(chalk.gray('    🔧 文件较大，使用 str_replace 工具进行精确修复'));
        }

        const suggestions = this.errorAnalyzer.generateErrorSuggestions(buildOutput);
        const prompt = this.stringReplaceTool.generatePrompt(
          filePath, fileContent, buildOutput, contextFiles, context, suggestions
        );

        const response = await this.callAI(prompt, {
          context: {
            taskType: 'str-replace',
            attemptNumber,
            phase: 'str-replace-generation',
            fileName: filePath
          }
        });

        const parseResult = this.stringReplaceTool.parseResponse(response, fileContent);

        if (parseResult.success) {
          // 执行字符串替换
          const result = await this.executeStringReplace(
            parseResult.filePath || filePath,
            parseResult.oldString,
            parseResult.newString,
            parseResult.expectedReplacements
          );

          this.currentFilePath = null;
          return result;
        } else {
          this.currentFilePath = null;
          return parseResult;
        }
      } else {
        const prompt = this.promptBuilder.buildFileFixPromptWithContext(
          filePath, fileContent, buildOutput, contextFiles, context
        );

        const response = await this.callAI(prompt, {
          context: {
            taskType: 'file-fix',
            attemptNumber,
            phase: 'fix-generation',
            fileName: filePath
          }
        });

        const result = this.parseFixResponse(response, fileContent);
        this.currentFilePath = null;
        return result;
      }
    } catch (error) {
      this.currentFilePath = null;

      return {
        success: false,
        error: error.message,
        aiResponse: null
      };
    }
  }

  /**
   * 解析修复响应
   */
  parseFixResponse(response, originalContent) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 开始解析AI响应...`));
        console.log(chalk.gray(`       响应长度: ${response.length} 字符`));
      }

      if (response.startsWith('<template>')) {
        return {
          success: true,
          newContent: this.response,
          fixedContent: this.response,
          format: 'vue',
          metadata: {}
        };
      }

      const parseResult = this.responseHandler.parseResponse(response);
      if (parseResult.success) {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ✅ 解析成功，格式: ${parseResult.format}`));
          console.log(chalk.gray(`       内容长度: ${parseResult.content.length} 字符`));
        }

        const validationResult = this.validateFixedContent(parseResult.content, originalContent, this.currentFilePath || '');
        if (validationResult) {
          return {
            success: true,
            newContent: validationResult,
            fixedContent: validationResult,
            format: parseResult.format,
            metadata: parseResult.metadata
          };
        }
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`       ❌ 使用PromptResponseHandler解析失败，尝试回退方案...`));
      }

      const extractedXml = this.responseHandler.extractFromCodeBlock(response);
      if (extractedXml.startsWith("<template>")) {
        return {
          success: true,
          aiResponse: extractedXml
        };
      }

      return this.parseFixResponseLegacy(extractedXml, originalContent);
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 修复响应失败'));
      if (this.options.verbose) {
        console.log(chalk.gray(`       错误详情: ${error.message}`));
      }
      return {
        success: false,
        error: `解析修复响应失败: ${error.message}`,
        aiResponse: response
      };
    }
  }

  /**
   * 旧版解析修复响应（向后兼容）
   */
  parseFixResponseLegacy(response, originalContent) {
    try {
      // 尝试解析运行时错误修复的特殊格式（优先级最高）
      const runtimeFixMatch = response.match(/<fix_result>\s*<fixed_content>([\s\S]*?)<\/fixed_content>\s*<changes_made>[\s\S]*?<\/changes_made>\s*<\/fix_result>/);

      if (runtimeFixMatch) {
        let content = runtimeFixMatch[1].trim();
        content = this.decodeHtmlEntities(content);

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent, this.currentFilePath || '');
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      }

      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<fix_result>[\s\S]*?<fixed_content>([\s\S]*?)<\/fixed_content>[\s\S]*?<\/fix_result>/);

      if (xmlMatch) {
        let content = xmlMatch[1].trim();
        content = this.decodeHtmlEntities(content);

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent, this.currentFilePath || '');
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      }

      // 回退：尝试解析代码块格式
      const codeBlockMatch = response.match(/```(?:vue|js|ts|javascript|typescript|xml)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        let content = codeBlockMatch[1].trim();

        // 检查代码块内容是否包含运行时修复格式
        const innerRuntimeFixMatch = content.match(/<fix_result>\s*<fixed_content>([\s\S]*?)<\/fixed_content>\s*<changes_made>[\s\S]*?<\/changes_made>\s*<\/fix_result>/);

        if (innerRuntimeFixMatch) {
          content = innerRuntimeFixMatch[1].trim();
          content = this.decodeHtmlEntities(content);
        }

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent, this.currentFilePath || '');
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      }

      return {
        success: false,
        error: '无法解析AI响应格式',
        aiResponse: response
      };
    } catch (error) {
      return {
        success: false,
        error: `解析修复响应失败: ${error.message}`,
        aiResponse: response
      };
    }
  }

  /**
   * 解码 HTML 实体
   */
  decodeHtmlEntities(text) {
    const entities = {
      '&lt;': '<',
      '&gt;': '>',
      '&amp;': '&',
      '&quot;': '"',
      '&#39;': "'",
      '&apos;': "'"
    };

    return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 验证修复后的内容
   */
  validateFixedContent(fixedContent, originalContent, filePath = '') {
    if (!fixedContent) {
      if (this.options.verbose) {
        console.log(chalk.gray('       ❌ 修复内容为空'));
      }
      return null;
    }

    const validator = new VueFileValidator({ verbose: this.options.verbose });
    const validationResult = validator.validateContent(fixedContent, originalContent, filePath);

    if (!validationResult.isValid) {
      if (this.options.verbose) {
        console.log(chalk.gray(`       ❌ 内容验证失败: ${validationResult.error}`));
      }
      return null;
    }

    if (this.options.verbose) {
      console.log(chalk.gray('       ✅ 内容验证通过'));
    }

    return validationResult.content;
  }

  /**
   * 获取之前的尝试记录
   */
  getPreviousAttempts(filesToFix, currentAttemptNumber) {
    const previousAttempts = [];

    // 从历史记录中获取之前的尝试
    for (let i = 1; i < currentAttemptNumber; i++) {
      const attemptKey = `attempt_${i}`;
      if (this.attemptHistory[attemptKey]) {
        this.attemptHistory[attemptKey].forEach(record => {
          if (filesToFix.includes(record.filePath)) {
            previousAttempts.push({
              attemptNumber: i,
              filePath: record.filePath,
              error: record.error,
              approach: record.approach || this.inferApproachFromError(record.error)
            });
          }
        });
      }
    }

    return previousAttempts;
  }

  /**
   * 记录失败的尝试
   */
  recordFailedAttempt(filePath, attemptNumber, error) {
    const attemptKey = `attempt_${attemptNumber}`;

    if (!this.attemptHistory[attemptKey]) {
      this.attemptHistory[attemptKey] = [];
    }

    this.attemptHistory[attemptKey].push({
      filePath,
      error,
      timestamp: new Date().toISOString(),
      approach: this.inferApproachFromError(error)
    });
  }

  /**
   * 从错误信息推断修复方法
   */
  inferApproachFromError(error) {
    if (!error || typeof error !== 'string') {
      return '未知方法';
    }

    const lowerError = error.toLowerCase();

    if (lowerError.includes('内容与原文件相同')) {
      return '小幅修改';
    } else if (lowerError.includes('缺少必要的文件结构')) {
      return '结构修复';
    } else if (lowerError.includes('内容过短')) {
      return '内容补充';
    } else if (lowerError.includes('语法错误') || lowerError.includes('syntax error')) {
      return '语法错误修复';
    } else if (lowerError.includes('导入') || lowerError.includes('import')) {
      return '导入语句修复';
    } else {
      return '通用修复';
    }
  }

  /**
   * 生成修复会话摘要
   */
  async generateSessionSummary() {
    try {
      const sessionId = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const summaryPath = path.join(this.options.logDir, `session-summary-${sessionId}.json`);

      // 读取所有相关的日志文件
      const logFiles = await this.getSessionLogFiles();
      const sessionData = {
        sessionId: sessionId,
        projectPath: this.projectPath,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        totalAttempts: this.fixStats.attempts,
        filesAnalyzed: this.fixStats.filesAnalyzed,
        filesModified: this.fixStats.filesModified,
        attempts: []
      };

      // 按轮次组织日志数据
      for (const logFile of logFiles) {
        try {
          const logData = await fs.readJson(logFile);
          const attemptNumber = logData.context?.attemptNumber || 1;

          if (!sessionData.attempts[attemptNumber - 1]) {
            sessionData.attempts[attemptNumber - 1] = {
              attemptNumber: attemptNumber,
              phases: []
            };
          }

          sessionData.attempts[attemptNumber - 1].phases.push({
            phase: logData.context?.phase || 'unknown',
            taskType: logData.context?.taskType || 'unknown',
            fileName: logData.context?.fileName || 'unknown',
            success: logData.success,
            duration_ms: logData.duration_ms,
            timestamp: logData.timestamp,
            logFile: path.basename(logFile)
          });
        } catch (error) {
          console.warn(chalk.yellow(`⚠️  无法读取日志文件: ${logFile}`));
        }
      }

      // 写入会话摘要
      await fs.writeJson(summaryPath, sessionData, { spaces: 2 });
      console.log(chalk.blue(`📊 会话摘要已生成: ${path.basename(summaryPath)}`));

      return summaryPath;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  生成会话摘要失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 获取会话相关的日志文件
   */
  async getSessionLogFiles() {
    try {
      // 确保日志目录存在
      await fs.ensureDir(this.options.logDir);

      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .map(file => path.join(this.options.logDir, file))
        .sort();

      if (this.options.verbose) {
        console.log(chalk.gray(`📁 找到 ${logFiles.length} 个 AI 调用日志文件`));

        // 显示最新的几个日志文件
        if (logFiles.length > 0) {
          const recentFiles = logFiles.slice(-3);
          console.log(chalk.gray('   最新日志文件:'));
          recentFiles.forEach(file => {
            console.log(chalk.gray(`   - ${path.basename(file)}`));
          });
        }
      }

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法读取日志目录 ${this.options.logDir}: ${error.message}`));

      // 尝试创建日志目录
      try {
        await fs.ensureDir(this.options.logDir);
        if (this.options.verbose) {
          console.log(chalk.green(`✅ 已创建日志目录: ${this.options.logDir}`));
        }
        return [];
      } catch (createError) {
        console.error(chalk.red(`❌ 无法创建日志目录: ${createError.message}`));
        return [];
      }
    }
  }

  /**
   * 获取修复统计信息
   */
  getFixStats() {
    return {
      ...this.fixStats,
      aiStats: this.getStats()
    };
  }

  /**
   * 列出所有轮次的日志文件
   */
  async listSessionLogs() {
    try {
      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .sort();

      if (logFiles.length === 0) {
        if (this.options.verbose) {
          console.log(chalk.gray('📝 没有找到 AI 调用日志文件'));
        }
        return [];
      }

      if (this.options.verbose) {
        console.log(chalk.blue(`📝 找到 ${logFiles.length} 个 AI 调用日志文件:`));
      }

      // 按轮次分组显示
      const attempts = {};
      for (const file of logFiles) {
        const match = file.match(/attempt(\d+)/);
        if (match) {
          const attemptNum = parseInt(match[1]);
          if (!attempts[attemptNum]) {
            attempts[attemptNum] = [];
          }
          attempts[attemptNum].push(file);
        }
      }

      // 显示每个轮次的日志
      Object.keys(attempts).sort((a, b) => parseInt(a) - parseInt(b)).forEach(attemptNum => {
        console.log(chalk.gray(`\n  轮次 ${attemptNum}:`));
        attempts[attemptNum].forEach(file => {
          console.log(chalk.gray(`    - ${file}`));
        });
      });

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法列出日志文件: ${error.message}`));
      return [];
    }
  }

  /**
   * 检查是否重复尝试相同的修复
   */
  isRepeatingAttempt(buildOutput) {
    // 生成当前错误的哈希
    const currentErrorHash = this.errorAnalyzer.generateErrorHash(buildOutput);

    // 检查是否已经尝试过相同的错误
    if (this.attemptHistory.errorHashes.includes(currentErrorHash)) {
      return true;
    }

    // 检查错误输出是否与上次完全相同
    if (this.attemptHistory.lastErrorOutput === buildOutput) {
      return true;
    }

    return false;
  }

  /**
   * 记录本次尝试
   */
  recordAttempt(buildOutput, filesToFix) {
    const errorHash = this.errorAnalyzer.generateErrorHash(buildOutput);

    this.attemptHistory.errorHashes.push(errorHash);
    this.attemptHistory.filesToFix.push([...filesToFix]);
    this.attemptHistory.lastErrorOutput = buildOutput;

    // 只保留最近3次尝试的记录
    if (this.attemptHistory.errorHashes.length > 3) {
      this.attemptHistory.errorHashes.shift();
      this.attemptHistory.filesToFix.shift();
    }
  }

  /**
   * 判断是否应该使用字符串替换工具
   * 基于文件大小、行数等因素决定
   */
  shouldUseStringReplace(fileContent, filePath) {
    if (!fileContent || typeof fileContent !== 'string') {
      return false;
    }

    const lines = fileContent.split('\n').length;
    const characters = fileContent.length;

    // 配置阈值
    const LINE_THRESHOLD = 500; // 超过1000行
    const CHAR_THRESHOLD = 50000; // 超过50KB

    // 检查文件大小
    const exceedsLineThreshold = lines > LINE_THRESHOLD;
    const exceedsCharThreshold = characters > CHAR_THRESHOLD;

    if (this.options.verbose && (exceedsLineThreshold || exceedsCharThreshold)) {
      console.log(chalk.gray(`    📊 文件统计: ${lines} 行, ${characters} 字符`));
      console.log(chalk.gray(`       超过行数阈值 (${LINE_THRESHOLD}): ${exceedsLineThreshold}`));
      console.log(chalk.gray(`       超过字符阈值 (${CHAR_THRESHOLD}): ${exceedsCharThreshold}`));
    }

    return exceedsLineThreshold || exceedsCharThreshold;
  }

  /**
   * 执行字符串替换操作
   */
  async executeStringReplace(filePath, oldString, newString, expectedReplacements = 1) {
    try {
      // 添加调试信息
      if (this.options.verbose) {
        console.log(chalk.gray('       🔍 执行字符串替换调试信息:'));
        console.log(chalk.gray(`       文件路径: ${filePath}`));
        console.log(chalk.gray(`       old_string 长度: ${oldString.length} 字符`));
        console.log(chalk.gray(`       old_string 前50字符: ${JSON.stringify(oldString.substring(0, 50))}`));
        console.log(chalk.gray(`       期望替换次数: ${expectedReplacements}`));
      }

      const result = await this.toolExecutor.executeToolCall('str_replace', {
        file_path: filePath,
        old_string: oldString,
        new_string: newString,
        expected_replacements: expectedReplacements
      });

      if (result.success) {
        if (this.options.verbose) {
          console.log(chalk.gray('       ✅ str_replace 执行成功'));
        }

        return {
          success: true,
          fixedContent: null, // str_replace 不返回完整内容
          newContent: null, // str_replace 不返回完整内容
          toolResult: result,
          method: 'str_replace'
        };
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ❌ str_replace 执行失败: ${result.error}`));

          // 添加更详细的调试信息
          if (result.error.includes('未找到要替换的文本')) {
            console.log(chalk.gray('       🔍 调试提示: 检查空格、缩进、换行符是否完全匹配'));
            console.log(chalk.gray('       📝 建议: 确保 old_string 包含足够的上下文以保证唯一匹配'));
          }
        }

        return {
          success: false,
          error: `str_replace 执行失败: ${result.error}`,
          toolResult: result
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `str_replace 执行异常: ${error.message}`
      };
    }
  }

}

module.exports = BuildFixAgent;
