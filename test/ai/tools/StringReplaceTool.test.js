const StringReplaceTool = require('../../../src/ai/tools/StringReplaceTool');

describe('StringReplaceTool', () => {
  let tool;

  beforeEach(() => {
    tool = new StringReplaceTool({ verbose: false });
  });

  describe('generatePrompt', () => {
    it('should generate a prompt with correct format', () => {
      const filePath = 'src/components/Test.vue';
      const fileContent = '<template>\n  <div>Test</div>\n</template>';
      const buildOutput = 'Error: Cannot resolve module';
      const context = { attemptNumber: 1 };

      const prompt = tool.generatePrompt(filePath, fileContent, buildOutput, {}, context, []);

      expect(prompt).toContain(filePath);
      expect(prompt).toContain('第 1 次');
      expect(prompt).toContain(fileContent);
      expect(prompt).toContain(buildOutput);
      expect(prompt).toContain('<tool_call>');
      expect(prompt).toContain('<name>str_replace</name>');
      expect(prompt).toContain('<parameters>');
      expect(prompt).toContain('<file_path>');
      expect(prompt).toContain('<old_string>');
      expect(prompt).toContain('<new_string>');
      expect(prompt).toContain('<expected_replacements>');
    });

    it('should truncate long files', () => {
      const filePath = 'src/components/LongFile.vue';
      const lines = Array(1500).fill('<div>line</div>');
      const fileContent = lines.join('\n');
      const buildOutput = 'Error: Test';
      const context = { attemptNumber: 1 };

      const prompt = tool.generatePrompt(filePath, fileContent, buildOutput, {}, context, []);

      expect(prompt).toContain('文件已截断，仅显示前1000行，总共1500行');
      expect(prompt.split('\n').length).toBeLessThan(fileContent.split('\n').length);
    });

    it('should include context files and previous attempts', () => {
      const filePath = 'src/components/Test.vue';
      const fileContent = '<template><div>Test</div></template>';
      const buildOutput = 'Error: Test';
      const contextFiles = { 'src/utils/helper.js': 'export function helper() {}' };
      const context = {
        attemptNumber: 2,
        previousAttempts: [
          { attemptNumber: 1, filePath, error: 'Syntax error', approach: '语法修复' }
        ]
      };

      const prompt = tool.generatePrompt(filePath, fileContent, buildOutput, contextFiles, context, []);

      expect(prompt).toContain('第 2 次');
      expect(prompt).toContain('src/utils/helper.js');
      expect(prompt).toContain('export function helper()');
      expect(prompt).toContain('1. 第1次尝试 - 语法修复: Syntax error');
    });
  });

  describe('parseResponse', () => {
    it('should parse standard tool call format correctly', () => {
      const response = `
<tool_call>
<name>str_replace</name>
<parameters>
<file_path>src/components/Test.vue</file_path>
<old_string>import { Vue } from 'vue'</old_string>
<new_string>import { defineComponent } from 'vue'</new_string>
<expected_replacements>1</expected_replacements>
</parameters>
</tool_call>
      `;

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(true);
      expect(result.format).toBe('tool_call');
      expect(result.filePath).toBe('src/components/Test.vue');
      expect(result.oldString).toBe('import { Vue } from \'vue\'');
      expect(result.newString).toBe('import { defineComponent } from \'vue\'');
      expect(result.expectedReplacements).toBe(1);
    });

    it('should parse tool call with short name tag', () => {
      const response = `
<tool_call>
<n>str_replace</n>
<parameters>
<file_path>src/test.js</file_path>
<old_string>old code</old_string>
<new_string>new code</new_string>
<expected_replacements>2</expected_replacements>
</parameters>
</tool_call>
      `;

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(true);
      expect(result.format).toBe('tool_call');
      expect(result.expectedReplacements).toBe(2);
    });

    it('should parse simple format for backward compatibility', () => {
      const response = `
<str_replace>
<old_string>const app = new Vue({})</old_string>
<new_string>const app = createApp({})</new_string>
</str_replace>
      `;

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(true);
      expect(result.format).toBe('simple');
      expect(result.filePath).toBe(null);
      expect(result.oldString).toBe('const app = new Vue({})');
      expect(result.newString).toBe('const app = createApp({})');
      expect(result.expectedReplacements).toBe(1);
    });

    it('should handle HTML entities in strings', () => {
      const response = `
<tool_call>
<name>str_replace</name>
<parameters>
<file_path>src/test.vue</file_path>
<old_string>&lt;div&gt;Test&lt;/div&gt;</old_string>
<new_string>&lt;div class=&quot;test&quot;&gt;Test&lt;/div&gt;</new_string>
<expected_replacements>1</expected_replacements>
</parameters>
</tool_call>
      `;

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(true);
      expect(result.oldString).toBe('<div>Test</div>');
      expect(result.newString).toBe('<div class="test">Test</div>');
    });

    it('should handle missing expected_replacements parameter', () => {
      const response = `
<tool_call>
<name>str_replace</name>
<parameters>
<file_path>src/test.js</file_path>
<old_string>old</old_string>
<new_string>new</new_string>
</parameters>
</tool_call>
      `;

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(true);
      expect(result.expectedReplacements).toBe(1); // default value
    });

    it('should fail when required parameters are missing', () => {
      const response = `
<tool_call>
<name>str_replace</name>
<parameters>
<file_path>src/test.js</file_path>
<old_string>old</old_string>
</parameters>
</tool_call>
      `;

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(false);
      expect(result.error).toContain('工具调用参数不完整');
    });

    it('should fail when no valid format is found', () => {
      const response = 'This is just plain text without any tool calls';

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(false);
      expect(result.error).toContain('未找到有效的 str_replace 工具调用格式');
    });

    it('should handle multiline strings correctly', () => {
      const response = `
<tool_call>
<name>str_replace</name>
<parameters>
<file_path>src/test.vue</file_path>
<old_string>export default {
  name: 'Test',
  data() {
    return {}
  }
}</old_string>
<new_string>export default defineComponent({
  name: 'Test',
  setup() {
    return {}
  }
})</new_string>
<expected_replacements>1</expected_replacements>
</parameters>
</tool_call>
      `;

      const result = tool.parseResponse(response, '');

      expect(result.success).toBe(true);
      expect(result.oldString).toContain('data() {');
      expect(result.newString).toContain('setup() {');
    });
  });

  describe('decodeHtmlEntities', () => {
    it('should decode common HTML entities', () => {
      expect(tool.decodeHtmlEntities('&lt;div&gt;')).toBe('<div>');
      expect(tool.decodeHtmlEntities('&amp;nbsp;')).toBe('&nbsp;');
      expect(tool.decodeHtmlEntities('&quot;test&quot;')).toBe('"test"');
      expect(tool.decodeHtmlEntities('&#39;test&#39;')).toBe("'test'");
      expect(tool.decodeHtmlEntities('&apos;test&apos;')).toBe("'test'");
    });

    it('should leave unknown entities unchanged', () => {
      expect(tool.decodeHtmlEntities('&unknown;')).toBe('&unknown;');
    });
  });

  describe('truncateOutput', () => {
    it('should not truncate short output', () => {
      const output = 'Short error message';
      expect(tool.truncateOutput(output)).toBe(output);
    });

    it('should truncate long output', () => {
      const output = 'x'.repeat(10000);
      const result = tool.truncateOutput(output);
      
      expect(result.length).toBeLessThan(output.length);
      expect(result).toContain('输出已截断');
    });

    it('should handle null/undefined input', () => {
      expect(tool.truncateOutput(null)).toBe('');
      expect(tool.truncateOutput(undefined)).toBe('');
      expect(tool.truncateOutput('')).toBe('');
    });
  });

  describe('formatContextFiles', () => {
    it('should format context files correctly', () => {
      const contextFiles = {
        'src/utils/helper.js': 'export function helper() {}',
        'src/types/index.ts': 'export interface User {}'
      };

      const result = tool.formatContextFiles(contextFiles);

      expect(result).toContain('**src/utils/helper.js**:');
      expect(result).toContain('export function helper()');
      expect(result).toContain('**src/types/index.ts**:');
      expect(result).toContain('export interface User');
    });

    it('should handle empty context files', () => {
      expect(tool.formatContextFiles({})).toBe('无相关文件');
      expect(tool.formatContextFiles(null)).toBe('无相关文件');
    });
  });

  describe('formatPreviousAttempts', () => {
    it('should format previous attempts correctly', () => {
      const attempts = [
        { attemptNumber: 1, approach: '语法修复', error: 'Syntax error' },
        { attemptNumber: 2, approach: '导入修复', error: 'Import error' }
      ];

      const result = tool.formatPreviousAttempts(attempts);

      expect(result).toContain('1. 第1次尝试 - 语法修复: Syntax error');
      expect(result).toContain('2. 第2次尝试 - 导入修复: Import error');
    });

    it('should handle empty attempts', () => {
      expect(tool.formatPreviousAttempts([])).toBe('无之前的尝试记录');
      expect(tool.formatPreviousAttempts(null)).toBe('无之前的尝试记录');
    });
  });
});
